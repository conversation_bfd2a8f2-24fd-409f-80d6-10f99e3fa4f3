#!/bin/bash

# Qwen2.5-VL 压力测试工具 - 使用 uv 管理 Python 依赖
# 支持完整的并发测试：2^0 到 2^10 (1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024)

set -e

echo "🚀 Qwen2.5-VL 压力测试工具 - uv 环境设置"
echo "=============================================="
echo "支持并发数: 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024"
echo ""

# 检查 uv 是否已安装
if ! command -v uv &> /dev/null; then
    echo "📦 安装 uv..."
    
    # 根据操作系统安装 uv
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            echo "使用 Homebrew 安装 uv..."
            brew install uv
        else
            echo "使用官方安装脚本安装 uv..."
            curl -LsSf https://astral.sh/uv/install.sh | sh
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        echo "使用官方安装脚本安装 uv..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
    else
        echo "❌ 不支持的操作系统，请手动安装 uv: https://github.com/astral-sh/uv"
        exit 1
    fi
    
    # 重新加载 PATH
    export PATH="$HOME/.cargo/bin:$PATH"
    
    echo "✅ uv 安装完成"
else
    echo "✅ uv 已安装: $(uv --version)"
fi

# 检查是否在项目目录中
if [ ! -f "Cargo.toml" ]; then
    echo "❌ 请在 stress_tool 目录中运行此脚本"
    exit 1
fi

# 初始化 Python 项目（如果还没有虚拟环境）
if [ ! -d ".venv" ]; then
    echo "🐍 创建 Python 虚拟环境..."
    uv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source .venv/bin/activate

# 安装 Python 依赖
echo "📦 安装 Python 分析依赖..."
uv pip install pandas matplotlib seaborn numpy requests click rich tabulate

echo ""
echo "✅ 环境设置完成！"
echo ""
echo "📋 使用说明:"
echo "============"
echo ""
echo "1. 🔨 构建 Rust 压力测试工具:"
echo "   cargo build --release"
echo ""
echo "2. 🧪 运行完整并发测试 (2^0 到 2^10):"
echo "   # 文本模式"
echo "   ./target/release/qwen-stress --mode text --max-concurrency-power 10 --duration 20 --output-file text_full_results.json"
echo ""
echo "   # 图像模式"
echo "   ./target/release/qwen-stress --mode image --max-concurrency-power 10 --duration 20 --output-file image_full_results.json"
echo ""
echo "   # 视频模式"
echo "   ./target/release/qwen-stress --mode video --max-concurrency-power 10 --duration 20 --output-file video_full_results.json"
echo ""
echo "   # 混合模态模式"
echo "   ./target/release/qwen-stress --mode mixed --max-concurrency-power 10 --duration 20 --output-file mixed_full_results.json"
echo ""
echo "3. 📊 运行性能分析:"
echo "   # 基础分析"
echo "   uv run python performance_analysis.py"
echo ""
echo "   # 高级分析"
echo "   uv run python advanced_analysis.py complete_multimodal_stress_results.json"
echo ""
echo "4. 🎯 并发级别说明:"
echo "   并发数序列: 1 → 2 → 4 → 8 → 16 → 32 → 64 → 128 → 256 → 512 → 1024"
echo "   对应幂次:   2^0 → 2^1 → 2^2 → 2^3 → 2^4 → 2^5 → 2^6 → 2^7 → 2^8 → 2^9 → 2^10"
echo ""
echo "5. 📈 性能监控:"
echo "   每个并发级别会测试指定时间（默认15秒）"
echo "   实时显示 QPS、成功率、响应时间等指标"
echo "   生成详细的 JSON 报告用于后续分析"
echo ""
echo "6. 🔍 结果分析:"
echo "   • QPS (每秒请求数)"
echo "   • 响应时间 (平均、最小、最大)"
echo "   • 成功率"
echo "   • 瓶颈识别"
echo "   • 性能建议"
echo ""
echo "🎉 准备就绪！现在可以开始压力测试了。"
