#!/bin/bash

# Qwen2.5-VL 压力测试工具 Python 环境设置脚本
# 使用 uv 来管理 Python 依赖

set -e

echo "🐍 设置 Qwen2.5-VL 压力测试工具 Python 环境"
echo "================================================"

# 检查 uv 是否已安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv 未安装，正在安装..."
    
    # 根据操作系统安装 uv
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install uv
        else
            curl -LsSf https://astral.sh/uv/install.sh | sh
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl -LsSf https://astral.sh/uv/install.sh | sh
    else
        echo "请手动安装 uv: https://github.com/astral-sh/uv"
        exit 1
    fi
    
    # 重新加载 PATH
    source ~/.bashrc 2>/dev/null || source ~/.zshrc 2>/dev/null || true
    
    echo "✅ uv 安装完成"
else
    echo "✅ uv 已安装: $(uv --version)"
fi

# 创建 Python 项目配置文件
echo "📝 创建 Python 项目配置..."

# 创建 pyproject.toml
cat > pyproject.toml << 'EOF'
[project]
name = "qwen-vl-stress-analysis"
version = "0.1.0"
description = "Performance analysis tools for Qwen2.5-VL stress testing"
authors = [
    {name = "Qwen2.5-VL Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "pandas>=1.3.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "numpy>=1.21.0",
    "requests>=2.25.0",
    "click>=8.0.0",
    "rich>=10.0.0",
    "tabulate>=0.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]

[project.scripts]
qwen-analyze = "analysis.main:cli"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
EOF

# 初始化 uv 项目
echo "🚀 初始化 uv 项目..."
uv init --no-readme --python 3.9

# 安装依赖
echo "📦 安装 Python 依赖..."
uv add pandas matplotlib seaborn numpy requests click rich tabulate

# 安装开发依赖
echo "🛠️  安装开发依赖..."
uv add --dev pytest black flake8 mypy

echo "✅ Python 环境设置完成！"
echo ""
echo "📋 使用说明:"
echo "  • 激活环境: source .venv/bin/activate"
echo "  • 运行分析: uv run python performance_analysis.py"
echo "  • 安装新包: uv add <package-name>"
echo "  • 运行测试: uv run pytest"
echo "  • 代码格式化: uv run black ."
echo ""
echo "🔧 可用命令:"
echo "  uv run python performance_analysis.py     # 运行性能分析"
echo "  uv run python advanced_analysis.py       # 运行高级分析"
echo "  uv run python generate_charts.py         # 生成性能图表"
echo ""
echo "📊 完整压力测试命令 (2^0 到 2^10):"
echo "  ./target/release/qwen-stress --mode text --max-concurrency-power 10 --duration 30"
echo "  ./target/release/qwen-stress --mode image --max-concurrency-power 10 --duration 30"
echo "  ./target/release/qwen-stress --mode video --max-concurrency-power 10 --duration 30"
echo "  ./target/release/qwen-stress --mode mixed --max-concurrency-power 10 --duration 30"
