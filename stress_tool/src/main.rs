use anyhow::Result;
use clap::Parser;
use log::{info, warn, error};
use std::path::PathBuf;
use std::time::{Duration, Instant};
use tokio::time::sleep;
use serde_json::{json, Value};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::process::Command;
use std::fs;

#[derive(Parser)]
#[command(name = "qwen-stress")]
#[command(about = "A stress testing tool for Qwen2.5-VL multimodal dialogue API")]
struct Args {
    /// Configuration file path
    #[arg(short, long, default_value = "config.toml")]
    config: PathBuf,

    /// API endpoint URL
    #[arg(short, long)]
    url: Option<String>,

    /// Test mode: text, image, video, mixed
    #[arg(short, long, default_value = "text")]
    mode: String,

    /// Maximum concurrent requests (2^n, where n=0-10)
    #[arg(long, default_value = "10")]
    max_concurrency_power: u8,

    /// Duration for each concurrency level in seconds
    #[arg(short, long, default_value = "30")]
    duration: u64,

    /// Request timeout in seconds
    #[arg(short, long, default_value = "30")]
    timeout: u64,

    /// Output format: json, csv, console
    #[arg(short, long, default_value = "console")]
    output: String,

    /// Output file path
    #[arg(long)]
    output_file: Option<PathBuf>,

    /// Verbose logging
    #[arg(short, long)]
    verbose: bool,
}

#[derive(Debug, Clone)]
struct TestMetrics {
    concurrency: usize,
    total_requests: Arc<AtomicU64>,
    successful_requests: Arc<AtomicU64>,
    failed_requests: Arc<AtomicU64>,
    total_response_time: Arc<AtomicU64>,
    total_ttft: Arc<AtomicU64>,  // Time to First Token累计
    min_ttft: Arc<AtomicU64>,    // 最小TTFT
    max_ttft: Arc<AtomicU64>,    // 最大TTFT
    start_time: Instant,
}

impl TestMetrics {
    fn new(concurrency: usize) -> Self {
        Self {
            concurrency,
            total_requests: Arc::new(AtomicU64::new(0)),
            successful_requests: Arc::new(AtomicU64::new(0)),
            failed_requests: Arc::new(AtomicU64::new(0)),
            total_response_time: Arc::new(AtomicU64::new(0)),
            total_ttft: Arc::new(AtomicU64::new(0)),
            min_ttft: Arc::new(AtomicU64::new(u64::MAX)),
            max_ttft: Arc::new(AtomicU64::new(0)),
            start_time: Instant::now(),
        }
    }

    fn record_request(&self, success: bool, response_time_ms: u64, ttft_ms: u64) {
        self.total_requests.fetch_add(1, Ordering::Relaxed);
        if success {
            self.successful_requests.fetch_add(1, Ordering::Relaxed);

            // 记录TTFT统计
            self.total_ttft.fetch_add(ttft_ms, Ordering::Relaxed);

            // 更新最小TTFT
            let mut current_min = self.min_ttft.load(Ordering::Relaxed);
            while ttft_ms < current_min {
                match self.min_ttft.compare_exchange_weak(
                    current_min, ttft_ms, Ordering::Relaxed, Ordering::Relaxed
                ) {
                    Ok(_) => break,
                    Err(x) => current_min = x,
                }
            }

            // 更新最大TTFT
            let mut current_max = self.max_ttft.load(Ordering::Relaxed);
            while ttft_ms > current_max {
                match self.max_ttft.compare_exchange_weak(
                    current_max, ttft_ms, Ordering::Relaxed, Ordering::Relaxed
                ) {
                    Ok(_) => break,
                    Err(x) => current_max = x,
                }
            }
        } else {
            self.failed_requests.fetch_add(1, Ordering::Relaxed);
        }
        self.total_response_time.fetch_add(response_time_ms, Ordering::Relaxed);
    }

    fn get_stats(&self) -> (f64, f64, f64, f64, f64, f64, f64) {
        let total = self.total_requests.load(Ordering::Relaxed);
        let successful = self.successful_requests.load(Ordering::Relaxed);
        let total_time = self.total_response_time.load(Ordering::Relaxed);
        let total_ttft = self.total_ttft.load(Ordering::Relaxed);
        let min_ttft = self.min_ttft.load(Ordering::Relaxed);
        let max_ttft = self.max_ttft.load(Ordering::Relaxed);
        let elapsed = self.start_time.elapsed().as_secs_f64();

        let success_rate = if total > 0 { successful as f64 / total as f64 } else { 0.0 };
        let qps = if elapsed > 0.0 { total as f64 / elapsed } else { 0.0 };
        let avg_response_time = if total > 0 { total_time as f64 / total as f64 } else { 0.0 };
        let avg_ttft = if successful > 0 { total_ttft as f64 / successful as f64 } else { 0.0 };
        let min_ttft_val = if min_ttft == u64::MAX { 0.0 } else { min_ttft as f64 };
        let max_ttft_val = max_ttft as f64;

        (success_rate, qps, avg_response_time, elapsed, avg_ttft, min_ttft_val, max_ttft_val)
    }
}

async fn send_request(endpoint: &str, mode: &str) -> Result<(bool, u64, u64)> {
    let start = Instant::now();

    let payload = match mode {
        "text" => json!({
            "model": "Qwen/Qwen2.5-VL-32B-Instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请简要解释什么是人工智能？"
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        "image" => json!({
            "model": "Qwen/Qwen2.5-VL-32B-Instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请描述这张图片的内容"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        "video" => json!({
            "model": "Qwen/Qwen2.5-VL-32B-Instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请分析这个视频的内容"
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        "mixed" => json!({
            "model": "Qwen/Qwen2.5-VL-32B-Instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请分析这些多模态内容并给出总结"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        _ => return Err(anyhow::anyhow!("Unknown mode: {}", mode)),
    };

    // Write payload to temporary file
    let temp_file = format!("/tmp/qwen_stress_payload_{}.json", rand::random::<u32>());
    fs::write(&temp_file, serde_json::to_string_pretty(&payload)?)?;

    // Use curl to send the request with timing information
    let output = Command::new("curl")
        .arg("-X")
        .arg("POST")
        .arg(endpoint)
        .arg("-H")
        .arg("Content-Type: application/json")
        .arg("-d")
        .arg(format!("@{}", temp_file))
        .arg("--max-time")
        .arg("30")
        .arg("--silent")
        .arg("--output")
        .arg("/dev/null")  // Discard response body
        .arg("--write-out")
        .arg("%{http_code},%{time_starttransfer},%{time_total}")  // HTTP状态码,首字节时间,总时间
        .output();

    // Clean up temp file
    let _ = fs::remove_file(&temp_file);

    match output {
        Ok(result) => {
            let duration = start.elapsed().as_millis() as u64;
            let output_str = String::from_utf8_lossy(&result.stdout);
            let parts: Vec<&str> = output_str.trim().split(',').collect();

            if parts.len() >= 3 {
                let status_code = parts[0];
                let time_starttransfer: f64 = parts[1].parse().unwrap_or(0.0);
                let time_total: f64 = parts[2].parse().unwrap_or(0.0);

                // 计算TTFT (首字节时间，单位转换为毫秒)
                let ttft_ms = (time_starttransfer * 1000.0) as u64;
                let total_time_ms = (time_total * 1000.0) as u64;

                // Check if status code is 2xx (success)
                let success = status_code.starts_with("2");

                if !success {
                    warn!("Request failed with HTTP status: {}", status_code);
                    if !result.stderr.is_empty() {
                        warn!("Error details: {}", String::from_utf8_lossy(&result.stderr));
                    }
                } else {
                    log::debug!("Request successful with status: {}, TTFT: {}ms, Total: {}ms",
                              status_code, ttft_ms, total_time_ms);
                }

                Ok((success, total_time_ms, ttft_ms))
            } else {
                warn!("Unexpected curl output format: {}", output_str);
                Ok((false, duration, 0))
            }
        }
        Err(e) => {
            let duration = start.elapsed().as_millis() as u64;
            warn!("Request error: {}", e);
            Ok((false, duration, 0))
        }
    }
}

async fn run_concurrency_level(
    endpoint: &str,
    mode: &str,
    concurrency: usize,
    duration_secs: u64,
) -> TestMetrics {
    let metrics = TestMetrics::new(concurrency);
    let semaphore = Arc::new(tokio::sync::Semaphore::new(concurrency));
    let end_time = Instant::now() + Duration::from_secs(duration_secs);

    info!("Starting concurrency level {} for {}s", concurrency, duration_secs);

    let mut tasks = Vec::new();

    while Instant::now() < end_time {
        if tasks.len() < concurrency * 2 {
            let endpoint = endpoint.to_string();
            let mode = mode.to_string();
            let metrics = metrics.clone();
            let semaphore = semaphore.clone();

            let task = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                match send_request(&endpoint, &mode).await {
                    Ok((success, response_time, ttft)) => {
                        metrics.record_request(success, response_time, ttft);
                    }
                    Err(e) => {
                        error!("Request failed: {}", e);
                        metrics.record_request(false, 0, 0);
                    }
                }
            });

            tasks.push(task);
        }

        // Clean up completed tasks
        tasks.retain(|task| !task.is_finished());

        tokio::time::sleep(Duration::from_millis(10)).await;
    }

    // Wait for remaining tasks with timeout
    let timeout_duration = Duration::from_secs(30);
    let remaining_tasks: Vec<_> = tasks.into_iter().collect();

    match tokio::time::timeout(timeout_duration, async {
        for task in remaining_tasks {
            let _ = task.await;
        }
    }).await {
        Ok(_) => info!("All tasks completed successfully"),
        Err(_) => warn!("Some tasks timed out during cleanup"),
    }

    metrics
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // Initialize logging
    if args.verbose {
        env_logger::Builder::from_default_env()
            .filter_level(log::LevelFilter::Debug)
            .init();
    } else {
        env_logger::Builder::from_default_env()
            .filter_level(log::LevelFilter::Info)
            .init();
    }

    info!("Starting Qwen2.5-VL stress testing tool");
    info!("Mode: {}", args.mode);
    info!("Max concurrency power: {}", args.max_concurrency_power);
    info!("Duration per level: {}s", args.duration);

    // Get endpoint URL
    let endpoint = args.url.unwrap_or_else(|| {
        "https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions".to_string()
    });

    info!("Using endpoint: {}", endpoint);

    // Test connectivity
    info!("Testing connectivity to endpoint...");
    match send_request(&endpoint, "text").await {
        Ok((success, response_time, ttft)) => {
            if success {
                info!("✅ Connectivity test successful ({}ms, TTFT: {}ms)", response_time, ttft);
            } else {
                warn!("⚠️ Connectivity test failed but endpoint is reachable");
            }
        }
        Err(e) => {
            error!("❌ Connectivity test failed: {}", e);
            return Err(e);
        }
    }

    // Run stress test phases
    let concurrency_levels: Vec<usize> = (0..=args.max_concurrency_power)
        .map(|n| 2_usize.pow(n as u32))
        .collect();

    let mut all_results = Vec::new();
    let test_start_time = std::time::SystemTime::now();

    for (phase, &concurrency) in concurrency_levels.iter().enumerate() {
        info!("Phase {}/{}: Testing with {} concurrent requests",
              phase + 1, concurrency_levels.len(), concurrency);

        let metrics = run_concurrency_level(&endpoint, &args.mode, concurrency, args.duration).await;
        let (success_rate, qps, avg_response_time, elapsed, avg_ttft, min_ttft, max_ttft) = metrics.get_stats();

        println!("\n{}", "=".repeat(80));
        println!("Concurrency Level: {}", concurrency);
        println!("{}", "=".repeat(80));
        println!("Duration: {:.2}s", elapsed);
        println!("Total Requests: {}", metrics.total_requests.load(Ordering::Relaxed));
        println!("Successful: {}", metrics.successful_requests.load(Ordering::Relaxed));
        println!("Failed: {}", metrics.failed_requests.load(Ordering::Relaxed));
        println!("Success Rate: {:.1}%", success_rate * 100.0);
        println!("TPS (Transactions/sec): {:.2}", qps);
        println!("QPS (Queries/sec): {:.2}", qps);
        println!("Avg Response Time: {:.2}ms", avg_response_time);
        println!("Avg TTFT (Time to First Token): {:.2}ms", avg_ttft);
        println!("Min TTFT: {:.2}ms", min_ttft);
        println!("Max TTFT: {:.2}ms", max_ttft);
        println!("{}", "=".repeat(80));

        // Store results for report
        all_results.push(json!({
            "concurrency": concurrency,
            "duration_seconds": elapsed,
            "total_requests": metrics.total_requests.load(Ordering::Relaxed),
            "successful_requests": metrics.successful_requests.load(Ordering::Relaxed),
            "failed_requests": metrics.failed_requests.load(Ordering::Relaxed),
            "success_rate": success_rate,
            "tps": qps,  // TPS (Transactions Per Second)
            "qps": qps,  // QPS (Queries Per Second) - 同一个值
            "requests_per_second": qps,
            "avg_response_time_ms": avg_response_time,
            "avg_ttft_ms": avg_ttft,  // Time to First Token
            "min_ttft_ms": min_ttft,
            "max_ttft_ms": max_ttft,
            "ttft_stats": {
                "average": avg_ttft,
                "minimum": min_ttft,
                "maximum": max_ttft
            }
        }));

        // Short cooldown between levels
        if phase < concurrency_levels.len() - 1 {
            info!("Cooling down for 3 seconds...");
            sleep(Duration::from_secs(3)).await;
        }
    }

    // Generate JSON report
    let total_requests: u64 = all_results.iter()
        .map(|r| r["total_requests"].as_u64().unwrap_or(0))
        .sum();
    let total_successful: u64 = all_results.iter()
        .map(|r| r["successful_requests"].as_u64().unwrap_or(0))
        .sum();
    let peak_qps = all_results.iter()
        .map(|r| r["requests_per_second"].as_f64().unwrap_or(0.0))
        .fold(0.0, f64::max);

    let report = json!({
        "test_summary": {
            "timestamp": test_start_time,
            "endpoint": endpoint,
            "mode": args.mode,
            "total_duration_seconds": all_results.len() as u64 * args.duration,
            "total_requests": total_requests,
            "total_successful": total_successful,
            "total_failed": total_requests - total_successful,
            "overall_success_rate": if total_requests > 0 { total_successful as f64 / total_requests as f64 } else { 0.0 },
            "peak_qps": peak_qps,
            "concurrency_levels_tested": concurrency_levels.len()
        },
        "concurrency_levels": all_results,
        "performance_curve": all_results.iter().map(|r| json!({
            "concurrency": r["concurrency"],
            "qps": r["requests_per_second"],
            "success_rate": r["success_rate"],
            "avg_response_time_ms": r["avg_response_time_ms"]
        })).collect::<Vec<_>>()
    });

    // Output report
    let report_json = serde_json::to_string_pretty(&report)?;

    if let Some(output_file) = args.output_file {
        std::fs::write(&output_file, &report_json)?;
        info!("📊 JSON report saved to: {:?}", output_file);
    } else {
        println!("\n📊 JSON Report:");
        println!("{}", report_json);
    }

    info!("🎉 Stress test completed successfully!");
    println!("\n✨ Test Summary:");
    println!("   Total Requests: {}", total_requests);
    println!("   Success Rate: {:.1}%", (total_successful as f64 / total_requests as f64) * 100.0);
    println!("   Peak QPS: {:.2}", peak_qps);

    Ok(())
}
