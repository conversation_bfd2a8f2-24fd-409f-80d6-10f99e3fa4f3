# Qwen2.5-VL 多模态对话压力测试工具

## 项目概述

这是一个基于Rust语言实现的Qwen2.5-VL多模态对话API压力测试工具，支持4种压测模式，具备并发控制、性能监控和详细报告生成功能。

## 🎯 功能特性

### 核心功能
- ✅ **4种压测模式**: 纯文本、图片+文本、视频+文本、混合模态
- ✅ **指数级并发测试**: 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024 并发数
- ✅ **实时性能监控**: QPS、响应时间、成功率实时显示
- ✅ **多格式报告**: Console、JSON、CSV输出格式
- ✅ **配置灵活**: 支持配置文件和命令行参数
- ✅ **异步高性能**: 基于Tokio异步运行时

### 技术架构
- **语言**: Rust 2021 Edition
- **异步运行时**: Tokio
- **HTTP客户端**: Reqwest (可选)
- **命令行解析**: Clap v4
- **配置管理**: TOML格式
- **日志系统**: env_logger

## 📁 项目结构

```
stress_tool/
├── Cargo.toml              # 项目配置和依赖
├── Dockerfile              # 容器化部署
├── Makefile                # 构建和测试脚本
├── README.md               # 详细使用说明
├── PROJECT_SUMMARY.md      # 项目总结（本文件）
├── config.toml             # 示例配置文件
├── setup_test_data.sh      # 测试数据设置脚本
├── src/                    # 源代码目录
│   ├── main.rs            # 主程序入口
│   ├── config.rs          # 配置管理模块
│   ├── client.rs          # HTTP客户端模块
│   ├── stress_test.rs     # 压力测试核心逻辑
│   ├── metrics.rs         # 性能指标收集
│   ├── report.rs          # 报告生成模块
│   └── multimodal.rs      # 多模态请求处理
└── target/                # 编译输出目录
    └── release/
        └── qwen-stress    # 可执行文件
```

## 🚀 快速开始

### 1. 构建项目

```bash
# 进入项目目录
cd stress_tool

# 构建发布版本
cargo build --release

# 或使用Makefile
make release
```

### 2. 基本使用

```bash
# 查看帮助
./target/release/qwen-stress --help

# 纯文本压测（演示模式）
./target/release/qwen-stress --mode text --max-concurrency-power 5 --duration 10

# 详细日志模式
./target/release/qwen-stress --mode text --verbose

# 自定义配置
./target/release/qwen-stress --config config.toml --mode mixed
```

### 3. 设置测试数据

```bash
# 运行数据设置脚本
./setup_test_data.sh

# 或使用Makefile
make setup-data
```

## ⚙️ 配置说明

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-c, --config` | 配置文件路径 | config.toml |
| `-u, --url` | API端点URL | - |
| `-m, --mode` | 测试模式 | text |
| `--max-concurrency-power` | 最大并发幂次(2^n) | 10 |
| `-d, --duration` | 每级别持续时间(秒) | 30 |
| `-t, --timeout` | 请求超时时间(秒) | 30 |
| `-o, --output` | 输出格式 | console |
| `--output-file` | 输出文件路径 | - |
| `-v, --verbose` | 详细日志 | false |

### 配置文件格式

```toml
[api]
endpoint = "http://localhost:8000/v1/chat/completions"
auth_token = "your-token-here"

[test]
max_concurrency_power = 10
duration_per_level = 30
request_timeout = 30
warmup_requests = 10
cooldown_seconds = 5

[data]
text_prompts = ["请解释深度学习", "什么是AI？"]
image_paths = ["data/images/sample1.jpg"]
video_paths = ["data/videos/sample1.mp4"]

[output]
format = "console"
real_time_display = true
```

## 📊 测试模式详解

### 1. 纯文本模式 (`--mode text`)
- 测试纯文本对话性能
- 最低资源消耗
- 适合基准性能测试

### 2. 图像模式 (`--mode image`)
- 测试图像分析能力
- 支持多种图像格式
- 随机选择1-3张图片

### 3. 视频模式 (`--mode video`)
- 测试视频分析能力
- 支持主流视频格式
- 通常单个视频请求

### 4. 混合模式 (`--mode mixed`)
- 综合测试多模态能力
- 图像+视频+文本组合
- 最全面的性能评估

## 📈 性能指标

### 收集的指标
- **请求速率**: QPS (Queries Per Second)
- **响应时间**: 平均值、P50、P95、P99百分位
- **成功率**: 成功请求百分比
- **错误分布**: 按错误类型分类统计
- **资源使用**: 字节传输量、Token消耗

### 瓶颈分析
- 高错误率检测 (成功率 < 95%)
- 高响应时间警告 (P95 > 5秒)
- QPS平台期识别
- 连接问题诊断

## 🛠️ 开发和扩展

### 当前实现状态

#### ✅ 已完成
- 基础项目结构
- 命令行参数解析
- 配置文件管理
- 演示模式实现
- 构建和部署脚本

#### 🚧 待完成（完整版本）
- HTTP客户端集成
- 多模态内容处理
- 实际API请求发送
- 详细指标收集
- 性能瓶颈分析
- JSON/CSV报告生成

### 扩展指南

1. **添加新的测试模式**
   - 在 `multimodal.rs` 中定义新的 `RequestType`
   - 实现对应的请求生成逻辑
   - 更新命令行参数解析

2. **集成新的指标**
   - 扩展 `metrics.rs` 中的指标收集
   - 添加新的性能分析算法
   - 更新报告生成模块

3. **支持新的输出格式**
   - 在 `report.rs` 中添加新的格式处理
   - 实现对应的序列化逻辑
   - 更新配置选项

## 🐳 容器化部署

### Docker构建

```bash
# 构建镜像
docker build -t qwen-vl-stress-tool .

# 运行容器
docker run --rm -it qwen-vl-stress-tool

# 使用Makefile
make docker-build
make docker-run
```

### 容器特性
- 基于Debian Bookworm Slim
- 包含ImageMagick和FFmpeg
- 自动设置测试数据
- 非root用户运行

## 📋 使用场景

### 1. API容量测试
```bash
./target/release/qwen-stress \
  --mode text \
  --max-concurrency-power 10 \
  --duration 60 \
  --output json \
  --output-file capacity_test.json
```

### 2. 多模态性能对比
```bash
for mode in text image video mixed; do
  ./target/release/qwen-stress \
    --mode $mode \
    --duration 30 \
    --output csv \
    --output-file ${mode}_results.csv
done
```

### 3. 长期稳定性测试
```bash
./target/release/qwen-stress \
  --mode mixed \
  --duration 300 \
  --max-concurrency-power 6 \
  --verbose
```

## 🔧 故障排除

### 常见问题

1. **编译错误**
   - 确保Rust版本 >= 1.70
   - 检查依赖版本兼容性
   - 使用 `cargo clean` 清理缓存

2. **运行时错误**
   - 检查配置文件格式
   - 验证API端点可访问性
   - 确认测试文件存在

3. **性能问题**
   - 调整并发级别
   - 增加超时时间
   - 检查系统资源限制

## 📝 开发日志

### v0.1.0 (当前版本)
- ✅ 基础项目架构
- ✅ 命令行界面
- ✅ 配置管理系统
- ✅ 演示模式实现
- ✅ 构建和部署脚本

### 计划中的版本

#### v0.2.0
- HTTP客户端集成
- 基础API请求功能
- 简单指标收集

#### v0.3.0
- 多模态内容处理
- 完整指标系统
- 瓶颈分析功能

#### v1.0.0
- 完整功能实现
- 性能优化
- 文档完善

## 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 实现新功能或修复
4. 添加测试用例
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见LICENSE文件

## 📞 支持

- 查看README.md获取详细使用说明
- 检查故障排除部分
- 启用详细日志进行调试
- 查看服务端API日志

---

**注意**: 当前版本为演示版本，展示了工具的基本架构和用户界面。完整的HTTP请求、多模态处理和详细指标收集功能需要进一步开发实现。
