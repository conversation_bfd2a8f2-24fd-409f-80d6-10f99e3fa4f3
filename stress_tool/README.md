# Qwen2.5-V<PERSON> Stress Testing Tool

A comprehensive stress testing tool for Qwen2.5-VL multimodal dialogue API, built with Rust and Tokio for high-performance concurrent testing.

## Features

- **4 Testing Modes**: Text-only, Image+Text, Video+Text, and Mixed multimodal testing
- **Scalable Concurrency**: Tests with exponentially increasing concurrency (2^0 to 2^10)
- **Comprehensive Metrics**: Response time percentiles, QPS, success rates, and error analysis
- **Multiple Output Formats**: Console, JSON, and CSV reporting
- **Real-time Monitoring**: Live performance metrics during testing
- **Bottleneck Analysis**: Automatic performance bottleneck detection and recommendations

## Installation

### Prerequisites

- Rust 1.70+ 
- Cargo package manager

### Build from Source

```bash
cd stress_tool
cargo build --release
```

The binary will be available at `target/release/qwen-stress`.

## Quick Start

### 1. Basic Text-only Testing

```bash
./target/release/qwen-stress \
  --url "http://localhost:8000/v1/chat/completions" \
  --mode text \
  --duration 30 \
  --max-concurrency-power 8
```

### 2. Image+Text Testing

First, prepare your test images and update the configuration:

```bash
# Edit config.toml to add image paths
./target/release/qwen-stress \
  --config config.toml \
  --mode image \
  --duration 60
```

### 3. Video+Text Testing

```bash
# Edit config.toml to add video paths
./target/release/qwen-stress \
  --config config.toml \
  --mode video \
  --duration 90
```

### 4. Mixed Multimodal Testing

```bash
./target/release/qwen-stress \
  --config config.toml \
  --mode mixed \
  --duration 120
```

## Configuration

### Configuration File (config.toml)

The tool uses a TOML configuration file for detailed settings:

```toml
[api]
endpoint = "http://localhost:8000/v1/chat/completions"
auth_token = "your-token-here"  # Optional

[test]
max_concurrency_power = 10      # Test up to 2^10 = 1024 concurrent requests
duration_per_level = 30         # 30 seconds per concurrency level
request_timeout = 30            # 30 second timeout per request
warmup_requests = 10            # 10 warmup requests
cooldown_seconds = 5            # 5 second cooldown between levels

[data]
text_prompts = [
    "请解释深度学习的基本概念",
    "什么是人工智能？",
    # ... more prompts
]
image_paths = [
    "data/images/sample1.jpg",
    "data/images/sample2.png"
]
video_paths = [
    "data/videos/sample1.mp4"
]

[output]
format = "console"              # "console", "json", or "csv"
file = "results.json"           # Optional output file
real_time_display = true        # Show live metrics
```

### Command Line Options

```bash
USAGE:
    qwen-stress [OPTIONS]

OPTIONS:
    -c, --config <CONFIG>                    Configuration file path [default: config.toml]
    -u, --url <URL>                         API endpoint URL
    -m, --mode <MODE>                       Test mode: text, image, video, mixed [default: text]
        --max-concurrency-power <POWER>     Maximum concurrent requests (2^n) [default: 10]
    -d, --duration <DURATION>               Duration per level in seconds [default: 30]
    -t, --timeout <TIMEOUT>                 Request timeout in seconds [default: 30]
    -o, --output <OUTPUT>                   Output format: json, csv, console [default: console]
        --output-file <FILE>                Output file path
    -v, --verbose                           Verbose logging
    -h, --help                              Print help information
```

## Test Modes

### 1. Text Mode (`--mode text`)
- Tests pure text conversations
- Uses prompts from `data.text_prompts` in config
- Lowest resource usage, good for baseline testing

### 2. Image Mode (`--mode image`)
- Tests image analysis with text prompts
- Requires images in `data.image_paths`
- Randomly selects 1-3 images per request
- Higher token usage due to image processing

### 3. Video Mode (`--mode video`)
- Tests video analysis with text prompts
- Requires videos in `data.video_paths`
- Usually one video per request
- Highest token usage due to video processing

### 4. Mixed Mode (`--mode mixed`)
- Tests combined image+video+text scenarios
- Uses predefined scenarios from `data.mixed_scenarios`
- Falls back to random combinations if no scenarios defined
- Most comprehensive testing mode

## Output Formats

### Console Output (Default)
Real-time progress display with final summary:

```
Phase 3/11: Testing with 4 concurrent requests
████████████████████████████████████████ 30/30s (QPS: 12.5, Success: 98.2%, Concurrency: 4)

================================================================================
Concurrency Level: 4
================================================================================
Duration: 30.00s
Total Requests: 375
Successful: 368 (98.1%)
Failed: 7
Requests/sec: 12.50
Avg Response Time: 320.15ms
P50 Response Time: 298.00ms
P95 Response Time: 567.00ms
P99 Response Time: 892.00ms
Total Bytes: 2.45 MB
Total Tokens: 15420
```

### JSON Output (`--output json`)
Structured data with detailed metrics:

```json
{
  "test_summary": {
    "timestamp": "2024-01-15T10:30:00Z",
    "total_duration_seconds": 330.0,
    "total_requests": 4250,
    "peak_qps": 45.2,
    "bottleneck_analysis": {
      "type": "High Response Time",
      "concurrency": 64,
      "recommendations": [
        "Response times are too high, consider reducing load",
        "Optimize server performance or scale horizontally"
      ]
    }
  },
  "concurrency_levels": [...],
  "performance_curve": [...]
}
```

### CSV Output (`--output csv`)
Tabular data suitable for analysis:

```csv
concurrency,duration_seconds,total_requests,successful_requests,requests_per_second,avg_response_time_ms
1,30.0,45,45,1.5,667
2,30.0,89,89,2.97,674
4,30.0,175,174,5.83,686
...
```

## Performance Analysis

### Metrics Collected

- **Request Rate**: Requests per second (QPS)
- **Response Times**: Average, P50, P95, P99 percentiles
- **Success Rate**: Percentage of successful requests
- **Error Distribution**: Categorized error types
- **Resource Usage**: Bytes transferred, tokens consumed
- **Request Types**: Distribution of different request types

### Bottleneck Detection

The tool automatically analyzes performance patterns and identifies:

1. **High Error Rate**: Success rate drops below 95%
2. **High Response Time**: P95 response time exceeds 5 seconds
3. **QPS Plateau**: Performance stops improving with higher concurrency
4. **Connection Issues**: Network-related failures

### Recommendations

Based on bottleneck analysis, the tool provides actionable recommendations:

- Concurrency optimization suggestions
- Server capacity planning advice
- Performance tuning recommendations
- Scaling strategy guidance

## Example Test Scenarios

### Scenario 1: API Capacity Testing
```bash
# Test maximum sustainable load
./target/release/qwen-stress \
  --mode text \
  --max-concurrency-power 10 \
  --duration 60 \
  --output json \
  --output-file capacity_test.json
```

### Scenario 2: Multimodal Performance Comparison
```bash
# Test different modalities
for mode in text image video mixed; do
  ./target/release/qwen-stress \
    --mode $mode \
    --duration 30 \
    --output csv \
    --output-file ${mode}_results.csv
done
```

### Scenario 3: Long-duration Stability Test
```bash
# Extended stability testing
./target/release/qwen-stress \
  --mode mixed \
  --duration 300 \
  --max-concurrency-power 6 \
  --verbose
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure the API server is running
   - Check the endpoint URL in configuration
   - Verify network connectivity

2. **High Error Rates**
   - Reduce concurrency levels
   - Increase request timeout
   - Check server logs for errors

3. **Out of Memory**
   - Reduce `max_concurrency_power`
   - Limit test duration
   - Use smaller test files

4. **File Not Found**
   - Verify image/video file paths in config
   - Ensure files exist and are readable
   - Use absolute paths if needed

### Debug Mode

Enable verbose logging for detailed information:

```bash
./target/release/qwen-stress --verbose
```

### Performance Tuning

For optimal performance:

1. **System Limits**: Increase file descriptor limits
2. **Network**: Tune TCP settings for high concurrency
3. **Memory**: Ensure sufficient RAM for concurrent requests
4. **Storage**: Use fast storage for test media files

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section
- Review configuration examples
- Enable verbose logging for debugging
- Check server-side logs for API errors
