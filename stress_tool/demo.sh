#!/bin/bash

# Qwen2.5-VL Stress Testing Tool Demo Script
# This script demonstrates the capabilities of the stress testing tool

set -e

echo "🚀 Qwen2.5-VL Stress Testing Tool Demo"
echo "======================================"
echo ""

# Check if binary exists
if [ ! -f "target/release/qwen-stress" ]; then
    echo "📦 Building the stress testing tool..."
    cargo build --release
    echo "✅ Build completed!"
    echo ""
fi

echo "📋 Tool Information:"
echo "-------------------"
./target/release/qwen-stress --help
echo ""

echo "🎯 Demo 1: Quick Text-only Stress Test"
echo "--------------------------------------"
echo "Testing with concurrency levels: 1, 2, 4, 8"
echo "Duration per level: 3 seconds"
echo ""
./target/release/qwen-stress \
    --mode text \
    --max-concurrency-power 3 \
    --duration 3 \
    --verbose

echo ""
echo "🎯 Demo 2: Image Mode Simulation"
echo "--------------------------------"
echo "Simulating image+text stress test"
echo ""
./target/release/qwen-stress \
    --mode image \
    --max-concurrency-power 2 \
    --duration 2

echo ""
echo "🎯 Demo 3: Video Mode Simulation"
echo "--------------------------------"
echo "Simulating video+text stress test"
echo ""
./target/release/qwen-stress \
    --mode video \
    --max-concurrency-power 2 \
    --duration 2

echo ""
echo "🎯 Demo 4: Mixed Multimodal Simulation"
echo "--------------------------------------"
echo "Simulating mixed multimodal stress test"
echo ""
./target/release/qwen-stress \
    --mode mixed \
    --max-concurrency-power 2 \
    --duration 2

echo ""
echo "📊 Demo 5: Different Output Formats"
echo "-----------------------------------"

# Console output (default)
echo "Console output (already shown above)"

# Simulate JSON output
echo ""
echo "JSON output simulation:"
echo '{'
echo '  "test_summary": {'
echo '    "timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'",'
echo '    "total_duration_seconds": 12.0,'
echo '    "total_requests": 156,'
echo '    "peak_qps": 8.5,'
echo '    "overall_success_rate": 0.967'
echo '  },'
echo '  "concurrency_levels": ['
echo '    {"concurrency": 1, "qps": 2.1, "success_rate": 0.98},'
echo '    {"concurrency": 2, "qps": 4.2, "success_rate": 0.96},'
echo '    {"concurrency": 4, "qps": 8.5, "success_rate": 0.95}'
echo '  ]'
echo '}'

echo ""
echo "CSV output simulation:"
echo "concurrency,qps,success_rate,avg_response_time_ms"
echo "1,2.1,0.98,245"
echo "2,4.2,0.96,267"
echo "4,8.5,0.95,289"

echo ""
echo "🔧 Demo 6: Configuration Options"
echo "--------------------------------"
echo "Available configuration options:"
echo ""
echo "Command line parameters:"
echo "  --mode: text, image, video, mixed"
echo "  --max-concurrency-power: 0-10 (2^n concurrent requests)"
echo "  --duration: seconds per concurrency level"
echo "  --timeout: request timeout in seconds"
echo "  --output: console, json, csv"
echo "  --verbose: detailed logging"
echo ""

echo "Configuration file (config.toml):"
cat << 'EOF'
[api]
endpoint = "http://localhost:8000/v1/chat/completions"

[test]
max_concurrency_power = 10
duration_per_level = 30
request_timeout = 30

[data]
text_prompts = ["请解释深度学习", "什么是AI？"]
image_paths = ["data/images/sample1.jpg"]
video_paths = ["data/videos/sample1.mp4"]

[output]
format = "console"
real_time_display = true
EOF

echo ""
echo "🏗️  Demo 7: Build and Development Tools"
echo "---------------------------------------"
echo "Available Makefile targets:"
echo ""
make help 2>/dev/null || echo "Run 'make help' to see available targets"

echo ""
echo "🐳 Demo 8: Docker Support"
echo "-------------------------"
echo "Docker commands:"
echo "  docker build -t qwen-vl-stress-tool ."
echo "  docker run --rm -it qwen-vl-stress-tool"
echo ""
echo "Or using Makefile:"
echo "  make docker-build"
echo "  make docker-run"

echo ""
echo "📈 Demo 9: Performance Analysis Features"
echo "----------------------------------------"
echo "The tool provides comprehensive performance analysis:"
echo ""
echo "Metrics collected:"
echo "  • Requests per second (QPS)"
echo "  • Response time percentiles (P50, P95, P99)"
echo "  • Success rate and error distribution"
echo "  • Resource usage (bytes, tokens)"
echo ""
echo "Bottleneck detection:"
echo "  • High error rate alerts (< 95% success)"
echo "  • High response time warnings (P95 > 5s)"
echo "  • QPS plateau identification"
echo "  • Connection issue diagnosis"
echo ""
echo "Recommendations:"
echo "  • Concurrency optimization suggestions"
echo "  • Server capacity planning advice"
echo "  • Performance tuning recommendations"

echo ""
echo "🎓 Demo 10: Usage Scenarios"
echo "---------------------------"
echo ""
echo "Scenario 1 - API Capacity Testing:"
echo "  ./target/release/qwen-stress --mode text --max-concurrency-power 8 --duration 60"
echo ""
echo "Scenario 2 - Multimodal Performance Comparison:"
echo "  for mode in text image video mixed; do"
echo "    ./target/release/qwen-stress --mode \$mode --duration 30 --output csv --output-file \${mode}_results.csv"
echo "  done"
echo ""
echo "Scenario 3 - Long-duration Stability Test:"
echo "  ./target/release/qwen-stress --mode mixed --duration 300 --max-concurrency-power 6"

echo ""
echo "✨ Demo Completed!"
echo "=================="
echo ""
echo "🎉 Congratulations! You've seen all the key features of the Qwen2.5-VL Stress Testing Tool."
echo ""
echo "📚 Next Steps:"
echo "  1. Read the detailed README.md for comprehensive usage instructions"
echo "  2. Check PROJECT_SUMMARY.md for technical details and architecture"
echo "  3. Customize config.toml for your specific testing needs"
echo "  4. Set up test data using ./setup_test_data.sh"
echo "  5. Run real stress tests against your Qwen2.5-VL API endpoint"
echo ""
echo "🔗 Key Files:"
echo "  • README.md - Detailed usage guide"
echo "  • PROJECT_SUMMARY.md - Technical overview"
echo "  • config.toml - Configuration template"
echo "  • Makefile - Build and test automation"
echo "  • Dockerfile - Container deployment"
echo ""
echo "💡 Remember: This is a demo version showing the tool's interface and capabilities."
echo "   The full implementation would include actual HTTP requests, multimodal processing,"
echo "   and detailed performance analysis."
echo ""
echo "🚀 Happy stress testing!"
