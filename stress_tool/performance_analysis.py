#!/usr/bin/env python3
"""
Qwen2.5-VL 多模态压力测试性能分析报告生成器
增强TPS指标分析，支持商业化部署决策
"""

import json
import statistics
from datetime import datetime
from typing import Dict, List, Any

def load_test_results():
    """加载所有测试结果"""
    results = {}
    
    try:
        with open('text_stress_results.json', 'r') as f:
            results['text'] = json.load(f)
    except FileNotFoundError:
        print("Warning: text_stress_results.json not found")
    
    try:
        with open('image_stress_results.json', 'r') as f:
            results['image'] = json.load(f)
    except FileNotFoundError:
        print("Warning: image_stress_results.json not found")
    
    try:
        with open('multimodal_stress_results.json', 'r') as f:
            results['mixed'] = json.load(f)
    except FileNotFoundError:
        print("Warning: multimodal_stress_results.json not found")
    
    return results

def generate_performance_comparison(results):
    """生成性能对比分析"""
    
    print("🔍 Qwen2.5-VL 多模态压力测试性能分析报告")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试端点: https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/")
    print()
    
    # 总体性能对比
    print("📊 总体性能对比")
    print("-" * 40)

    for mode, data in results.items():
        if not data:
            continue

        summary = data['test_summary']
        levels = data.get('concurrency_levels', [])

        # 计算增强的TPS指标
        peak_individual_tps = summary['peak_qps']  # 单请求TPS (峰值)

        # 计算并发级别总TPS
        concurrent_level_tps = []
        for level in levels:
            duration = level.get('duration_seconds', 0)
            successful = level.get('successful_requests', 0)
            if duration > 0:
                level_aggregate_tps = successful / duration
                concurrent_level_tps.append(level_aggregate_tps)

        max_concurrent_tps = max(concurrent_level_tps) if concurrent_level_tps else 0

        # 计算系统整体TPS
        total_duration = summary.get('total_duration_seconds', 0)
        system_overall_tps = summary['total_requests'] / total_duration if total_duration > 0 else 0

        # 估算首字延迟 (TTFT) - 基于响应时间的经验估算
        if levels:
            avg_response_time = sum(level['avg_response_time_ms'] for level in levels) / len(levels)
            estimated_ttft = avg_response_time * 0.2  # 估算为20%
        else:
            estimated_ttft = 0

        print(f"{mode.upper()} 模式:")
        print(f"  总请求数: {summary['total_requests']}")
        print(f"  成功率: {summary['overall_success_rate']*100:.1f}%")
        print(f"  系统整体TPS: {system_overall_tps:.2f} (整个测试期间)")
        print(f"  并发级别最大TPS: {max_concurrent_tps:.2f} (单个并发级别系统吞吐)")
        print(f"  单请求峰值TPS: {peak_individual_tps:.2f} (用户体验指标)")
        print(f"  估算首字延迟(TTFT): {estimated_ttft:.0f}ms")
        print(f"  测试时长: {summary['total_duration_seconds']}秒")
        print()
    
    # 并发性能分析
    print("🚀 并发性能分析")
    print("-" * 40)
    
    # 创建性能对比表格
    comparison_data = []
    
    for mode, data in results.items():
        if not data:
            continue
            
        for level in data['concurrency_levels']:
            comparison_data.append({
                'Mode': mode,
                'Concurrency': level['concurrency'],
                'QPS': level['requests_per_second'],
                'Avg_Response_Time_ms': level['avg_response_time_ms'],
                'Success_Rate': level['success_rate'] * 100,
                'Total_Requests': level['total_requests']
            })
    
    if comparison_data:
        # 按并发数分组显示
        concurrency_groups = {}
        for item in comparison_data:
            conc = item['Concurrency']
            if conc not in concurrency_groups:
                concurrency_groups[conc] = []
            concurrency_groups[conc].append(item)

        for concurrency in sorted(concurrency_groups.keys()):
            print(f"并发数 {concurrency}:")
            for item in concurrency_groups[concurrency]:
                print(f"  {item['Mode']:>6}: QPS={item['QPS']:>6.2f}, "
                      f"响应时间={item['Avg_Response_Time_ms']:>7.0f}ms, "
                      f"成功率={item['Success_Rate']:>5.1f}%")
            print()
    
    # 性能趋势分析
    print("📈 性能趋势分析")
    print("-" * 40)
    
    for mode, data in results.items():
        if not data:
            continue
            
        levels = data['concurrency_levels']
        if len(levels) < 2:
            continue
            
        print(f"{mode.upper()} 模式趋势:")
        
        # QPS趋势
        qps_values = [level['requests_per_second'] for level in levels]
        qps_trend = "上升" if qps_values[-1] > qps_values[0] else "下降"
        qps_change = ((qps_values[-1] - qps_values[0]) / qps_values[0]) * 100
        print(f"  QPS趋势: {qps_trend} ({qps_change:+.1f}%)")
        
        # 响应时间趋势
        rt_values = [level['avg_response_time_ms'] for level in levels]
        rt_trend = "增加" if rt_values[-1] > rt_values[0] else "减少"
        rt_change = ((rt_values[-1] - rt_values[0]) / rt_values[0]) * 100
        print(f"  响应时间趋势: {rt_trend} ({rt_change:+.1f}%)")
        
        # 找到最佳并发数
        best_qps_idx = qps_values.index(max(qps_values))
        best_concurrency = levels[best_qps_idx]['concurrency']
        print(f"  最佳并发数: {best_concurrency} (QPS: {max(qps_values):.2f})")
        print()
    
    # 模式对比分析
    print("🔄 模式对比分析")
    print("-" * 40)
    
    if len(results) > 1:
        # 找出每种模式的最佳性能
        best_performance = {}
        for mode, data in results.items():
            if not data:
                continue
            levels = data['concurrency_levels']
            best_qps = max(level['requests_per_second'] for level in levels)
            best_level = next(level for level in levels if level['requests_per_second'] == best_qps)
            best_performance[mode] = {
                'qps': best_qps,
                'response_time': best_level['avg_response_time_ms'],
                'concurrency': best_level['concurrency']
            }
        
        # 排序显示
        sorted_modes = sorted(best_performance.items(), key=lambda x: x[1]['qps'], reverse=True)
        
        print("最佳QPS排名:")
        for i, (mode, perf) in enumerate(sorted_modes, 1):
            print(f"  {i}. {mode.upper()}: {perf['qps']:.2f} QPS "
                  f"(并发{perf['concurrency']}, 响应时间{perf['response_time']:.0f}ms)")
        print()
        
        # 响应时间对比
        print("平均响应时间对比 (最佳并发下):")
        sorted_by_rt = sorted(best_performance.items(), key=lambda x: x[1]['response_time'])
        for i, (mode, perf) in enumerate(sorted_by_rt, 1):
            print(f"  {i}. {mode.upper()}: {perf['response_time']:.0f}ms")
        print()
    
    # 性能建议
    print("💡 性能优化建议")
    print("-" * 40)
    
    for mode, data in results.items():
        if not data:
            continue
            
        levels = data['concurrency_levels']
        qps_values = [level['requests_per_second'] for level in levels]
        rt_values = [level['avg_response_time_ms'] for level in levels]
        
        print(f"{mode.upper()} 模式:")
        
        # 找到QPS开始下降的点
        peak_qps_idx = qps_values.index(max(qps_values))
        optimal_concurrency = levels[peak_qps_idx]['concurrency']
        
        if peak_qps_idx < len(levels) - 1:
            print(f"  建议并发数: {optimal_concurrency} (QPS开始下降前的最佳点)")
        else:
            print(f"  建议并发数: {optimal_concurrency} (可尝试更高并发)")
        
        # 响应时间分析
        avg_rt = sum(rt_values) / len(rt_values)
        if avg_rt > 5000:
            print(f"  ⚠️  响应时间较高 ({avg_rt:.0f}ms)，建议检查服务器性能")
        elif avg_rt > 2000:
            print(f"  ℹ️  响应时间中等 ({avg_rt:.0f}ms)，可考虑优化")
        else:
            print(f"  ✅ 响应时间良好 ({avg_rt:.0f}ms)")
        
        print()
    
    # 总结
    print("📋 测试总结")
    print("-" * 40)
    
    total_requests = sum(data['test_summary']['total_requests'] for data in results.values() if data)
    total_successful = sum(data['test_summary']['total_successful'] for data in results.values() if data)
    overall_success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
    
    print(f"总测试请求数: {total_requests}")
    print(f"总成功请求数: {total_successful}")
    print(f"整体成功率: {overall_success_rate:.1f}%")
    print()
    
    if len(results) > 1:
        best_mode = max(results.items(), 
                       key=lambda x: x[1]['test_summary']['peak_qps'] if x[1] else 0)
        print(f"最佳性能模式: {best_mode[0].upper()} (峰值QPS: {best_mode[1]['test_summary']['peak_qps']:.2f})")
    
    print()
    print("🎯 关键发现:")
    
    # 分析关键发现
    if 'image' in results and 'text' in results and results['image'] and results['text']:
        image_qps = results['image']['test_summary']['peak_qps']
        text_qps = results['text']['test_summary']['peak_qps']
        if image_qps > text_qps:
            improvement = ((image_qps - text_qps) / text_qps) * 100
            print(f"  • 图像模式比文本模式性能更好 (+{improvement:.1f}%)")
        else:
            decline = ((text_qps - image_qps) / text_qps) * 100
            print(f"  • 图像处理对性能有影响 (-{decline:.1f}%)")
    
    if 'mixed' in results and results['mixed']:
        mixed_qps = results['mixed']['test_summary']['peak_qps']
        print(f"  • 混合模态模式峰值QPS: {mixed_qps:.2f}")
        
        # 与其他模式对比
        other_modes = {k: v for k, v in results.items() if k != 'mixed' and v}
        if other_modes:
            avg_other_qps = sum(data['test_summary']['peak_qps'] for data in other_modes.values()) / len(other_modes)
            if mixed_qps < avg_other_qps:
                impact = ((avg_other_qps - mixed_qps) / avg_other_qps) * 100
                print(f"  • 多模态处理对性能有一定影响 (-{impact:.1f}%)")
    
    print(f"  • 服务器在测试期间表现稳定，成功率达到 {overall_success_rate:.1f}%")
    print(f"  • Qwen/Qwen2.5-VL-32B-Instruct 模型响应正常")

def main():
    """主函数"""
    results = load_test_results()
    
    if not any(results.values()):
        print("❌ 未找到测试结果文件，请先运行压力测试")
        return
    
    generate_performance_comparison(results)
    
    # 保存分析结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'performance_analysis_{timestamp}.txt'
    
    print(f"\n📄 分析报告已生成")
    print(f"如需保存报告，请运行: python performance_analysis.py > {output_file}")

if __name__ == '__main__':
    main()
