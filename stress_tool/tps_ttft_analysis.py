#!/usr/bin/env python3
"""
Qwen2.5-VL TPS和TTFT专项性能分析工具
专注于事务处理速度(TPS)和首字延迟(TTFT)分析
"""

import json
import sys
from datetime import datetime
from typing import Dict, List, Any

def load_test_results(file_path: str) -> Dict[str, Any]:
    """加载测试结果JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return {}

def analyze_tps_performance(data: Dict[str, Any]) -> None:
    """TPS (Transactions Per Second) 性能分析"""
    print("💰 TPS (事务处理速度) 分析")
    print("=" * 60)
    
    levels = data.get('concurrency_levels', [])
    if not levels:
        print("❌ 未找到并发级别数据")
        return
    
    print(f"{'并发数':<8} {'TPS':<10} {'QPS':<10} {'吞吐量等级':<12}")
    print("-" * 50)
    
    peak_tps = 0
    peak_concurrency = 0
    tps_data = []
    
    for level in levels:
        concurrency = level['concurrency']
        tps = level.get('tps', level['requests_per_second'])
        qps = level['requests_per_second']
        
        # TPS性能等级评估
        if tps >= 2.0:
            grade = "优秀 🌟"
        elif tps >= 1.0:
            grade = "良好 ✅"
        elif tps >= 0.5:
            grade = "一般 ⚠️"
        else:
            grade = "较低 ❌"
        
        print(f"{concurrency:<8} {tps:<10.2f} {qps:<10.2f} {grade:<12}")
        
        tps_data.append((concurrency, tps))
        if tps > peak_tps:
            peak_tps = tps
            peak_concurrency = concurrency
    
    print()
    print("📈 TPS性能趋势分析:")
    print(f"  • 峰值TPS: {peak_tps:.2f} (并发数: {peak_concurrency})")
    
    # 计算TPS增长率
    if len(tps_data) >= 2:
        initial_tps = tps_data[0][1]
        final_tps = tps_data[-1][1]
        growth_rate = ((final_tps - initial_tps) / initial_tps) * 100 if initial_tps > 0 else 0
        print(f"  • TPS增长率: {growth_rate:+.1f}% (从{initial_tps:.2f}到{final_tps:.2f})")
        
        # 找到TPS增长最快的区间
        max_growth = 0
        best_interval = None
        for i in range(len(tps_data) - 1):
            current_growth = tps_data[i+1][1] - tps_data[i][1]
            if current_growth > max_growth:
                max_growth = current_growth
                best_interval = (tps_data[i][0], tps_data[i+1][0])
        
        if best_interval:
            print(f"  • 最佳扩展区间: {best_interval[0]} → {best_interval[1]} 并发 (+{max_growth:.2f} TPS)")
    
    # TPS稳定性分析
    tps_values = [item[1] for item in tps_data]
    if len(tps_values) > 1:
        tps_variance = sum((x - sum(tps_values)/len(tps_values))**2 for x in tps_values) / len(tps_values)
        tps_std = tps_variance ** 0.5
        stability = "稳定" if tps_std < 0.5 else "波动较大"
        print(f"  • TPS稳定性: {stability} (标准差: {tps_std:.2f})")
    
    print()

def analyze_ttft_performance(data: Dict[str, Any]) -> None:
    """TTFT (Time to First Token) 首字延迟分析"""
    print("⚡ TTFT (首字延迟) 分析")
    print("=" * 60)
    
    levels = data.get('concurrency_levels', [])
    if not levels:
        print("❌ 未找到并发级别数据")
        return
    
    print(f"{'并发数':<8} {'平均TTFT(ms)':<12} {'最小TTFT(ms)':<12} {'最大TTFT(ms)':<12} {'延迟等级':<10}")
    print("-" * 70)
    
    ttft_data = []
    best_ttft = float('inf')
    worst_ttft = 0
    best_concurrency = 0
    worst_concurrency = 0
    
    for level in levels:
        concurrency = level['concurrency']
        avg_ttft = level.get('avg_ttft_ms', 0)
        min_ttft = level.get('min_ttft_ms', 0)
        max_ttft = level.get('max_ttft_ms', 0)
        
        if avg_ttft == 0:
            continue
        
        # TTFT性能等级评估
        if avg_ttft <= 1000:
            grade = "极快 🚀"
        elif avg_ttft <= 3000:
            grade = "快速 ✅"
        elif avg_ttft <= 5000:
            grade = "一般 ⚠️"
        elif avg_ttft <= 10000:
            grade = "较慢 🐌"
        else:
            grade = "很慢 ❌"
        
        print(f"{concurrency:<8} {avg_ttft:<12.0f} {min_ttft:<12.0f} {max_ttft:<12.0f} {grade:<10}")
        
        ttft_data.append((concurrency, avg_ttft, min_ttft, max_ttft))
        
        if avg_ttft < best_ttft:
            best_ttft = avg_ttft
            best_concurrency = concurrency
        
        if avg_ttft > worst_ttft:
            worst_ttft = avg_ttft
            worst_concurrency = concurrency
    
    if not ttft_data:
        print("❌ 未找到TTFT数据")
        return
    
    print()
    print("📊 TTFT性能分析:")
    print(f"  • 最佳TTFT: {best_ttft:.0f}ms (并发数: {best_concurrency})")
    print(f"  • 最差TTFT: {worst_ttft:.0f}ms (并发数: {worst_concurrency})")
    
    # 计算TTFT统计
    avg_ttfts = [item[1] for item in ttft_data]
    overall_avg_ttft = sum(avg_ttfts) / len(avg_ttfts)
    print(f"  • 整体平均TTFT: {overall_avg_ttft:.0f}ms")
    
    # TTFT与并发数的关系
    if len(ttft_data) >= 2:
        initial_ttft = ttft_data[0][1]
        final_ttft = ttft_data[-1][1]
        ttft_change = ((final_ttft - initial_ttft) / initial_ttft) * 100 if initial_ttft > 0 else 0
        trend = "增加" if ttft_change > 0 else "减少"
        print(f"  • TTFT变化趋势: {trend} {abs(ttft_change):.1f}% (随并发数增加)")
    
    # TTFT一致性分析
    ttft_ranges = [(item[3] - item[2]) for item in ttft_data if item[2] > 0 and item[3] > 0]
    if ttft_ranges:
        avg_range = sum(ttft_ranges) / len(ttft_ranges)
        consistency = "一致" if avg_range < 2000 else "波动较大"
        print(f"  • TTFT一致性: {consistency} (平均波动范围: {avg_range:.0f}ms)")
    
    print()

def analyze_tps_ttft_correlation(data: Dict[str, Any]) -> None:
    """TPS与TTFT相关性分析"""
    print("🔗 TPS与TTFT相关性分析")
    print("=" * 60)
    
    levels = data.get('concurrency_levels', [])
    if not levels:
        return
    
    correlation_data = []
    for level in levels:
        tps = level.get('tps', level['requests_per_second'])
        ttft = level.get('avg_ttft_ms', 0)
        if ttft > 0:
            correlation_data.append((tps, ttft, level['concurrency']))
    
    if len(correlation_data) < 2:
        print("❌ 数据不足，无法进行相关性分析")
        return
    
    print(f"{'TPS':<8} {'TTFT(ms)':<10} {'并发数':<8} {'效率评估':<12}")
    print("-" * 50)
    
    for tps, ttft, concurrency in correlation_data:
        # 效率评估：TPS/TTFT比值越高越好
        efficiency = (tps * 1000) / ttft if ttft > 0 else 0
        
        if efficiency >= 0.5:
            efficiency_grade = "高效 🌟"
        elif efficiency >= 0.2:
            efficiency_grade = "良好 ✅"
        elif efficiency >= 0.1:
            efficiency_grade = "一般 ⚠️"
        else:
            efficiency_grade = "低效 ❌"
        
        print(f"{tps:<8.2f} {ttft:<10.0f} {concurrency:<8} {efficiency_grade:<12}")
    
    print()
    
    # 寻找最佳平衡点
    best_balance = max(correlation_data, key=lambda x: (x[0] * 1000) / x[1] if x[1] > 0 else 0)
    print(f"💡 最佳TPS/TTFT平衡点:")
    print(f"  • 并发数: {best_balance[2]}")
    print(f"  • TPS: {best_balance[0]:.2f}")
    print(f"  • TTFT: {best_balance[1]:.0f}ms")
    print(f"  • 效率比: {(best_balance[0] * 1000) / best_balance[1]:.3f}")
    print()

def generate_recommendations(data: Dict[str, Any]) -> None:
    """生成TPS和TTFT优化建议"""
    print("💡 性能优化建议")
    print("=" * 60)
    
    levels = data.get('concurrency_levels', [])
    if not levels:
        return
    
    # 分析TPS
    tps_values = [level.get('tps', level['requests_per_second']) for level in levels]
    peak_tps = max(tps_values)
    peak_idx = tps_values.index(peak_tps)
    optimal_concurrency = levels[peak_idx]['concurrency']
    
    # 分析TTFT
    ttft_values = [level.get('avg_ttft_ms', 0) for level in levels if level.get('avg_ttft_ms', 0) > 0]
    avg_ttft = sum(ttft_values) / len(ttft_values) if ttft_values else 0
    
    print("🎯 TPS优化建议:")
    if peak_tps >= 2.0:
        print(f"  ✅ TPS性能优秀 ({peak_tps:.2f})，建议在{optimal_concurrency}并发下部署")
    elif peak_tps >= 1.0:
        print(f"  ⚠️  TPS性能良好 ({peak_tps:.2f})，可考虑以下优化：")
        print("     • 增加GPU资源以提升并行处理能力")
        print("     • 优化模型推理引擎配置")
    else:
        print(f"  ❌ TPS性能较低 ({peak_tps:.2f})，需要重点优化：")
        print("     • 检查服务器资源配置")
        print("     • 考虑模型量化或优化")
        print("     • 增加实例数量进行负载分担")
    
    print()
    print("⚡ TTFT优化建议:")
    if avg_ttft <= 3000:
        print(f"  ✅ TTFT表现优秀 ({avg_ttft:.0f}ms)，用户体验良好")
    elif avg_ttft <= 5000:
        print(f"  ⚠️  TTFT表现一般 ({avg_ttft:.0f}ms)，建议优化：")
        print("     • 优化模型加载和初始化流程")
        print("     • 使用更快的推理引擎")
    else:
        print(f"  ❌ TTFT较慢 ({avg_ttft:.0f}ms)，严重影响用户体验：")
        print("     • 考虑使用更小的模型或量化版本")
        print("     • 实现预热机制减少冷启动时间")
        print("     • 优化网络和I/O性能")
    
    print()
    print("🚀 综合部署建议:")
    print(f"  • 推荐并发数: {optimal_concurrency}")
    print(f"  • 预期TPS: {peak_tps:.2f}")
    print(f"  • 预期TTFT: {avg_ttft:.0f}ms")
    
    if peak_tps >= 1.0 and avg_ttft <= 5000:
        print("  • 🌟 系统性能良好，适合生产环境部署")
    else:
        print("  • ⚠️  建议先进行性能优化再部署到生产环境")

def main():
    """主函数"""
    print("📊 Qwen2.5-VL TPS & TTFT 专项性能分析")
    print("=" * 80)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查是否提供了文件路径
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "tps_ttft_test_results.json"
    
    # 加载测试结果
    data = load_test_results(file_path)
    if not data:
        print("❌ 无法加载测试数据，请检查文件路径")
        return
    
    print(f"📁 分析文件: {file_path}")
    print()
    
    # 执行各项分析
    analyze_tps_performance(data)
    analyze_ttft_performance(data)
    analyze_tps_ttft_correlation(data)
    generate_recommendations(data)
    
    print("✅ TPS & TTFT 分析完成！")

if __name__ == '__main__':
    main()
