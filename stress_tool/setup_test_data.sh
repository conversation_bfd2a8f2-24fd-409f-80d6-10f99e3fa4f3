#!/bin/bash

# Setup script for Qwen2.5-VL stress test data
# This script creates the directory structure and downloads sample test files

set -e

echo "Setting up Qwen2.5-VL stress test data..."

# Create directory structure
mkdir -p data/{images,videos,mixed}

# Function to download file if it doesn't exist
download_if_not_exists() {
    local url="$1"
    local output="$2"
    
    if [ ! -f "$output" ]; then
        echo "Downloading $output..."
        curl -L -o "$output" "$url" || {
            echo "Warning: Failed to download $url"
            return 1
        }
    else
        echo "$output already exists, skipping..."
    fi
}

# Download sample images (using placeholder images)
echo "Setting up sample images..."

# Create sample images using ImageMagick (if available) or download placeholders
if command -v convert >/dev/null 2>&1; then
    echo "Creating sample images with ImageMagick..."
    
    # Create different types of test images
    convert -size 800x600 xc:lightblue -pointsize 48 -fill black \
        -gravity center -annotate +0+0 "Sample Image 1\nFor Qwen2.5-VL Testing" \
        data/images/sample1.jpg
    
    convert -size 1200x800 xc:lightgreen -pointsize 36 -fill darkgreen \
        -gravity center -annotate +0+0 "High Resolution\nTest Image 2" \
        data/images/sample2.png
    
    convert -size 640x480 xc:lightyellow -pointsize 32 -fill red \
        -gravity center -annotate +0+0 "Standard Resolution\nTest Image 3" \
        data/images/sample3.jpeg
    
    # Create a chart-like image
    convert -size 1000x600 xc:white -fill blue -stroke blue -strokewidth 2 \
        -draw "line 100,500 900,100" -fill red -stroke red \
        -draw "line 100,450 900,200" -pointsize 24 -fill black \
        -gravity north -annotate +0+50 "Performance Chart" \
        data/mixed/chart.png
    
    # Create a diagram-like image
    convert -size 800x600 xc:white -fill lightblue -stroke blue -strokewidth 3 \
        -draw "rectangle 100,100 300,200" -draw "rectangle 500,100 700,200" \
        -draw "line 300,150 500,150" -pointsize 18 -fill black \
        -annotate +150+160 "Input" -annotate +550+160 "Output" \
        -gravity north -annotate +0+50 "System Diagram" \
        data/mixed/diagram.jpg
        
    echo "Sample images created successfully!"
else
    echo "ImageMagick not found. You can:"
    echo "1. Install ImageMagick: brew install imagemagick (macOS) or apt-get install imagemagick (Ubuntu)"
    echo "2. Manually add your own test images to data/images/ directory"
    echo "3. Use the tool with text-only mode"
fi

# Create sample video files (using ffmpeg if available)
echo "Setting up sample videos..."

if command -v ffmpeg >/dev/null 2>&1; then
    echo "Creating sample videos with ffmpeg..."
    
    # Create a simple test video (10 seconds, 30fps)
    ffmpeg -f lavfi -i testsrc=duration=10:size=640x480:rate=30 \
        -f lavfi -i sine=frequency=1000:duration=10 \
        -c:v libx264 -c:a aac -shortest data/videos/sample1.mp4 -y
    
    # Create another test video with different properties
    ffmpeg -f lavfi -i testsrc2=duration=15:size=1280x720:rate=24 \
        -f lavfi -i sine=frequency=500:duration=15 \
        -c:v libx264 -c:a aac -shortest data/videos/sample2.avi -y
    
    # Create a mixed scenario video
    ffmpeg -f lavfi -i testsrc=duration=20:size=800x600:rate=25 \
        -f lavfi -i sine=frequency=800:duration=20 \
        -c:v libx264 -c:a aac -shortest data/mixed/presentation.mp4 -y
        
    echo "Sample videos created successfully!"
else
    echo "ffmpeg not found. You can:"
    echo "1. Install ffmpeg: brew install ffmpeg (macOS) or apt-get install ffmpeg (Ubuntu)"
    echo "2. Manually add your own test videos to data/videos/ directory"
    echo "3. Use the tool with text-only or image-only modes"
fi

# Update config.toml with the created file paths
echo "Updating configuration file..."

cat > config.toml << 'EOF'
# Qwen2.5-VL Stress Test Configuration

[api]
endpoint = "http://localhost:8000/v1/chat/completions"

[api.headers]
"Content-Type" = "application/json"
"User-Agent" = "qwen-vl-stress-tool/0.1.0"

[test]
max_concurrency_power = 10
duration_per_level = 30
request_timeout = 30
warmup_requests = 10
cooldown_seconds = 5

[data]
text_prompts = [
    "请解释深度学习的基本概念",
    "什么是人工智能？请详细介绍",
    "请描述机器学习的应用场景",
    "如何优化神经网络的性能？",
    "请介绍自然语言处理的发展历程",
    "什么是计算机视觉？它有哪些应用？",
    "请解释强化学习的基本原理",
    "大语言模型是如何工作的？",
    "请分析人工智能的发展趋势",
    "什么是多模态AI？它的优势是什么？",
    "请分析这张图片的内容",
    "请描述图片中的主要元素",
    "这个图表显示了什么信息？",
    "请总结视频的主要内容",
    "视频中展示了哪些关键信息？",
    "请分析视频的技术细节"
]

image_paths = [
    "data/images/sample1.jpg",
    "data/images/sample2.png",
    "data/images/sample3.jpeg"
]

video_paths = [
    "data/videos/sample1.mp4",
    "data/videos/sample2.avi"
]

[[data.mixed_scenarios]]
text = "请分析这张图片和视频中的内容，并比较它们的异同点"
images = ["data/mixed/chart.png"]
videos = ["data/mixed/presentation.mp4"]

[[data.mixed_scenarios]]
text = "请根据提供的多媒体内容，生成一份详细的分析报告"
images = ["data/mixed/diagram.jpg", "data/images/sample1.jpg"]
videos = ["data/videos/sample1.mp4"]

[[data.mixed_scenarios]]
text = "请解释图表中的数据趋势，并结合视频内容进行分析"
images = ["data/mixed/chart.png", "data/mixed/diagram.jpg"]
videos = ["data/mixed/presentation.mp4"]

[output]
format = "console"
include_raw_data = false
real_time_display = true
EOF

echo "Configuration updated!"

# Create a simple test script
cat > run_tests.sh << 'EOF'
#!/bin/bash

# Quick test script for different modes

echo "Building the stress testing tool..."
cargo build --release

echo "Running text-only test..."
./target/release/qwen-stress --mode text --duration 10 --max-concurrency-power 3

echo "Running image+text test..."
./target/release/qwen-stress --mode image --duration 15 --max-concurrency-power 3

echo "Running video+text test..."
./target/release/qwen-stress --mode video --duration 20 --max-concurrency-power 2

echo "Running mixed multimodal test..."
./target/release/qwen-stress --mode mixed --duration 25 --max-concurrency-power 2

echo "All tests completed!"
EOF

chmod +x run_tests.sh

# Create directory structure info
cat > data/README.md << 'EOF'
# Test Data Directory Structure

This directory contains test data for the Qwen2.5-VL stress testing tool.

## Directory Structure

```
data/
├── images/          # Test images for image+text mode
│   ├── sample1.jpg
│   ├── sample2.png
│   └── sample3.jpeg
├── videos/          # Test videos for video+text mode
│   ├── sample1.mp4
│   └── sample2.avi
└── mixed/           # Mixed scenario test files
    ├── chart.png
    ├── diagram.jpg
    └── presentation.mp4
```

## File Requirements

### Images
- Supported formats: JPG, PNG, JPEG, GIF, BMP
- Recommended size: 640x480 to 1920x1080
- File size: < 10MB per image

### Videos
- Supported formats: MP4, AVI, MOV, MKV
- Recommended duration: 10-60 seconds
- Recommended resolution: 720p or 1080p
- File size: < 100MB per video

## Adding Your Own Test Files

1. Place image files in the `images/` directory
2. Place video files in the `videos/` directory
3. Update the `config.toml` file to reference your files
4. Ensure files are accessible and not corrupted

## Sample File Generation

The `setup_test_data.sh` script can generate sample files using:
- ImageMagick for sample images
- ffmpeg for sample videos

Install these tools for automatic sample generation:
```bash
# macOS
brew install imagemagick ffmpeg

# Ubuntu/Debian
sudo apt-get install imagemagick ffmpeg
```
EOF

echo ""
echo "Setup completed successfully!"
echo ""
echo "Directory structure created:"
echo "  data/images/     - Test images"
echo "  data/videos/     - Test videos" 
echo "  data/mixed/      - Mixed scenario files"
echo ""
echo "Next steps:"
echo "1. Build the tool: cargo build --release"
echo "2. Run quick tests: ./run_tests.sh"
echo "3. Customize config.toml for your API endpoint"
echo "4. Add your own test files to data/ directories"
echo ""
echo "For detailed usage, see README.md"
